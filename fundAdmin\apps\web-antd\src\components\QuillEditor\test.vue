<template>
  <div style="padding: 20px;">
    <h2>富文本编辑器测试</h2>
    
    <QuillEditor
      v-model="content"
      placeholder="请输入测试内容..."
      :height="400"
      @change="handleChange"
    />
    
    <div style="margin-top: 20px;">
      <h3>输出内容：</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ content }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import QuillEditor from './index.vue';

const content = ref('<p>这是初始内容</p>');

function handleChange(newContent) {
  console.log('内容变化:', newContent);
}
</script>
