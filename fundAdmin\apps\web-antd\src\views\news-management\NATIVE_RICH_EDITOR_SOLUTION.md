# 原生富文本编辑器解决方案

## 问题背景

由于项目使用了workspace配置，无法通过npm/pnpm安装第三方富文本编辑器依赖。因此采用原生HTML5 contentEditable + document.execCommand API实现了一个功能完整的富文本编辑器。

## 技术方案

### 1. 核心技术
- **HTML5 contentEditable**: 提供可编辑的内容区域
- **document.execCommand**: 执行富文本格式化命令
- **Vue 3 Composition API**: 组件逻辑实现
- **CSS3**: 现代化的样式设计

### 2. 架构设计
```
QuillEditor/
├── index.vue          # 主组件
├── test.vue           # 测试组件
└── README.md          # 使用说明
```

## 功能特性

### 1. 文本格式化
- ✅ **粗体** (Ctrl+B)
- ✅ **斜体** (Ctrl+I) 
- ✅ **下划线** (Ctrl+U)
- ✅ **删除线**
- ✅ 字号设置 (小/正常/大/特大)
- ✅ 标题格式 (H1-H4)

### 2. 段落对齐
- ✅ 左对齐
- ✅ 居中对齐
- ✅ 右对齐

### 3. 列表功能
- ✅ 无序列表
- ✅ 有序列表

### 4. 媒体插入
- ✅ 插入链接
- ✅ 插入图片 (支持本地上传)
- ✅ 图片自动预览

### 5. 编辑操作
- ✅ 撤销 (Ctrl+Z)
- ✅ 重做 (Ctrl+Y / Ctrl+Shift+Z)
- ✅ 清除格式
- ✅ 复制粘贴 (保持格式)

### 6. 用户体验
- ✅ 键盘快捷键支持
- ✅ 工具栏状态反馈
- ✅ 占位符提示
- ✅ 响应式设计
- ✅ 与Ant Design风格统一

## 组件接口

### Props
```typescript
interface Props {
  modelValue?: string;      // 双向绑定内容
  placeholder?: string;     // 占位符文本
  readonly?: boolean;       // 只读模式
  height?: string | number; // 编辑器高度
}
```

### Events
```typescript
emits: {
  'update:modelValue': [value: string];  // 内容更新
  'change': [value: string];             // 内容变化
}
```

### 暴露方法
```typescript
defineExpose({
  getHTML: () => string,           // 获取HTML内容
  getText: () => string,           // 获取纯文本
  setHTML: (html: string) => void, // 设置HTML内容
  focus: () => void,               // 聚焦编辑器
  blur: () => void,                // 失焦编辑器
  execCommand: (cmd, val?) => void // 执行编辑命令
});
```

## 使用示例

### 基础使用
```vue
<template>
  <QuillEditor
    v-model="content"
    placeholder="请输入内容..."
    :height="400"
    @change="handleChange"
  />
</template>

<script setup>
import QuillEditor from '#/components/QuillEditor/index.vue';

const content = ref('');

function handleChange(newContent) {
  console.log('内容变化:', newContent);
}
</script>
```

### 高级使用
```vue
<template>
  <QuillEditor
    ref="editorRef"
    v-model="content"
    :readonly="isReadonly"
    :height="500"
  />
  
  <div>
    <button @click="insertCustomContent">插入自定义内容</button>
    <button @click="getPlainText">获取纯文本</button>
  </div>
</template>

<script setup>
const editorRef = ref();

function insertCustomContent() {
  editorRef.value?.setHTML('<p><strong>自定义内容</strong></p>');
}

function getPlainText() {
  const text = editorRef.value?.getText();
  console.log('纯文本:', text);
}
</script>
```

## 样式设计

### 1. 工具栏设计
- 分组布局，逻辑清晰
- 按钮状态反馈 (hover/active)
- 下拉选择器样式统一
- 分隔线区分功能区域

### 2. 编辑区域
- 最小高度200px，最大高度500px
- 自动滚动，内容超出时可滚动
- 占位符样式优化
- 内容样式预设 (标题、段落、列表等)

### 3. 响应式适配
- 工具栏自动换行
- 移动端友好的按钮尺寸
- 触摸设备优化

## 技术优势

### 1. 零依赖
- 不需要安装任何第三方库
- 避免了依赖冲突问题
- 减小了项目体积

### 2. 高性能
- 原生API，性能优异
- 无额外的JavaScript框架开销
- 快速响应用户操作

### 3. 可定制性
- 完全控制样式和行为
- 易于扩展新功能
- 与项目风格完美融合

### 4. 兼容性
- 支持所有现代浏览器
- HTML5标准API
- 渐进式增强

## 局限性与解决方案

### 1. document.execCommand已废弃
**问题**: 部分浏览器可能在未来版本中移除支持
**解决方案**: 
- 当前所有主流浏览器仍完全支持
- 可在未来升级到Selection API + Range API
- 或集成成熟的富文本编辑器库

### 2. 功能相对简单
**问题**: 相比专业富文本编辑器功能较少
**解决方案**:
- 满足新闻编辑的基本需求
- 可根据需要逐步扩展功能
- 保持简洁易用的设计理念

### 3. 浏览器差异
**问题**: 不同浏览器的execCommand行为可能略有差异
**解决方案**:
- 针对主流浏览器进行测试
- 添加兼容性处理代码
- 提供降级方案

## 扩展计划

### 1. 短期扩展
- [ ] 表格插入和编辑
- [ ] 代码块语法高亮
- [ ] 更多文本颜色选项
- [ ] 字体选择器

### 2. 中期扩展
- [ ] 图片拖拽上传
- [ ] 视频嵌入支持
- [ ] 数学公式编辑
- [ ] 全屏编辑模式

### 3. 长期规划
- [ ] 协作编辑功能
- [ ] 版本历史管理
- [ ] 插件系统
- [ ] 移动端优化

## 总结

通过原生技术实现的富文本编辑器，在满足项目需求的同时，避免了依赖管理的复杂性。虽然功能相对简单，但完全满足新闻内容编辑的需求，并且具有良好的扩展性和可维护性。

这个解决方案证明了在某些场景下，原生技术仍然是最佳选择，特别是在需要避免依赖冲突或对性能有严格要求的项目中。
