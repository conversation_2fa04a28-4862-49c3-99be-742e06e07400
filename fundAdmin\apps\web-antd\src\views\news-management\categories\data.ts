import type { VxeGridPropTypes } from '#/adapter/vxe-table';

import { $t } from '@vben/locales';

// 表格列配置
export function useColumns(): VxeGridPropTypes.Columns {
  return [
    {
      type: 'checkbox',
      width: 50,
    },
    {
      field: 'id',
      title: 'ID',
      width: 80,
      sortable: true,
    },
    {
      field: 'name',
      title: '分类名称',
      minWidth: 150,
      slots: { default: 'name' },
    },
    {
      field: 'slug',
      title: '分类标识',
      width: 120,
      showOverflow: 'tooltip',
    },
    {
      field: 'description',
      title: '描述',
      minWidth: 200,
      slots: { default: 'description' },
    },
    {
      field: 'sortOrder',
      title: '排序',
      width: 80,
      sortable: true,
    },
    {
      field: 'isActive',
      title: '状态',
      width: 80,
      slots: { default: 'isActive' },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      field: 'updatedAt',
      title: '更新时间',
      width: 160,
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      title: $t('ui.actionTitle.action'),
      width: 120,
      fixed: 'right',
      slots: { default: 'action' },
      cellRender: {
        name: 'CellActions',
        props: {
          actions: [
            {
              code: 'edit',
              text: '编辑',
              icon: 'lucide:edit',
            },
            {
              code: 'delete',
              text: '删除',
              icon: 'lucide:trash-2',
              color: 'error',
            },
          ],
        },
      },
    },
  ];
}

// 搜索表单配置
export function useGridFormSchema() {
  return [
    {
      field: 'search',
      title: '搜索',
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入分类名称或标识',
        clearable: true,
      },
      itemProps: {
        labelWidth: 60,
      },
    },
    {
      field: 'isActive',
      title: '状态',
      component: 'VbenSelect',
      componentProps: {
        placeholder: '请选择状态',
        clearable: true,
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
      itemProps: {
        labelWidth: 60,
      },
    },
  ];
}

// 分类状态配置
export const CATEGORY_STATUS_CONFIG = {
  true: {
    text: '启用',
    color: 'green',
  },
  false: {
    text: '禁用',
    color: 'red',
  },
} as const;
