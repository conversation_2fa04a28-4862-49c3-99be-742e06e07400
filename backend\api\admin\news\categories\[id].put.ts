import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员更新新闻分类接口
 * PUT /api/admin/news/categories/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法更新分类'
    //   });
    // }

    // 获取分类ID
    const categoryId = getRouterParam(event, 'id');
    if (!categoryId || isNaN(Number(categoryId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的分类ID'
      });
    }

    // 检查分类是否存在
    const existingCategory = await query(
      'SELECT * FROM news_categories WHERE id = ?',
      [categoryId]
    );

    if (existingCategory.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '分类不存在'
      });
    }

    // 获取请求体数据
    const body = await readBody(event);
    const { name, slug, description, parent_id, sort_order, is_active } = body;

    // 构建更新字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (name !== undefined) {
      if (!name || typeof name !== 'string' || !name.trim()) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类名称不能为空'
        });
      }

      const trimmedName = name.trim();
      if (trimmedName.length > 100) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类名称不能超过100个字符'
        });
      }

      // 检查名称是否与其他分类重复
      const existingNameCategory = await query(
        'SELECT id FROM news_categories WHERE name = ? AND id != ?',
        [trimmedName, categoryId]
      );

      if (existingNameCategory.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类名称已存在'
        });
      }

      updateFields.push('name = ?');
      updateValues.push(trimmedName);
    }

    if (slug !== undefined) {
      if (!slug || typeof slug !== 'string' || !slug.trim()) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类标识不能为空'
        });
      }

      const trimmedSlug = slug.trim();
      if (!/^[a-z0-9-]+$/.test(trimmedSlug)) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类标识只能包含小写字母、数字和连字符'
        });
      }

      // 检查slug是否与其他分类重复
      const existingSlugCategory = await query(
        'SELECT id FROM news_categories WHERE slug = ? AND id != ?',
        [trimmedSlug, categoryId]
      );

      if (existingSlugCategory.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '分类标识已存在'
        });
      }

      updateFields.push('slug = ?');
      updateValues.push(trimmedSlug);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description || null);
    }

    if (parent_id !== undefined) {
      // 如果指定了父分类，验证父分类是否存在且不是自己
      if (parent_id && parent_id !== null) {
        if (Number(parent_id) === Number(categoryId)) {
          throw createError({
            statusCode: 400,
            statusMessage: '不能将分类设置为自己的父分类'
          });
        }

        const parentCategory = await query(
          'SELECT id FROM news_categories WHERE id = ? AND is_active = 1',
          [parent_id]
        );

        if (parentCategory.length === 0) {
          throw createError({
            statusCode: 400,
            statusMessage: '指定的父分类不存在或已禁用'
          });
        }
      }

      updateFields.push('parent_id = ?');
      updateValues.push(parent_id || null);
    }

    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(sort_order || 0);
    }

    if (is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(is_active !== false ? 1 : 0);
    }

    // 如果没有要更新的字段
    if (updateFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '没有要更新的字段'
      });
    }

    // 添加更新时间
    updateFields.push('updated_at = NOW()');

    // 执行更新
    updateValues.push(categoryId);
    await query(
      `UPDATE news_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 查询更新后的分类信息
    const updatedCategory = await query(
      `SELECT 
        id, name, slug, description, parent_id, sort_order, is_active, created_at, updated_at
       FROM news_categories WHERE id = ?`,
      [categoryId]
    );

    // 记录审计日志
    await logAdminAction(admin.id, 'category:update', '更新新闻分类', {
      categoryId,
      categoryName: updatedCategory[0].name,
      changes: body
    });

    return {
      success: true,
      message: '分类更新成功',
      data: {
        id: updatedCategory[0].id,
        name: updatedCategory[0].name,
        slug: updatedCategory[0].slug,
        description: updatedCategory[0].description,
        parentId: updatedCategory[0].parent_id,
        sortOrder: updatedCategory[0].sort_order,
        isActive: updatedCategory[0].is_active === 1,
        createdAt: updatedCategory[0].created_at,
        updatedAt: updatedCategory[0].updated_at
      }
    };

  } catch (error: any) {
    logger.error('更新新闻分类失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '更新分类失败'
    });
  }
});
