<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message } from 'ant-design-vue';

import {
  createCategory,
  updateCategory,
  getAllCategories,
  type NewsManagementApi,
} from '#/api/news-management';

defineOptions({
  name: 'NewsCategoryForm',
});

// 抽屉API
const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange: async (isOpen) => {
    if (isOpen) {
      // 加载父分类选项
      await loadParentCategories();
      
      // 获取编辑数据
      const data = drawerApi.getData<NewsManagementApi.NewsCategory>();
      console.log('分类编辑数据:', data);
      
      formApi.resetForm();
      
      if (data && Object.keys(data).length > 0) {
        console.log('设置分类编辑数据');
        id.value = data.id;
        formApi.setValues({
          name: data.name || '',
          slug: data.slug || '',
          description: data.description || '',
          parent_id: data.parent_id || data.parentId || null,
          sort_order: data.sort_order || data.sortOrder || 0,
          is_active: data.is_active !== undefined ? data.is_active : (data.isActive !== undefined ? data.isActive : true),
        });
      } else {
        console.log('新增分类模式');
        id.value = undefined;
      }
    }
  },
});

// 分类ID，用于判断是新增还是编辑
const id = ref<number | undefined>();

const isUpdate = computed(() => !!id.value);

// 标题
const title = computed(() => 
  isUpdate.value ? '编辑分类' : '新增分类'
);

// 父分类选项
const parentCategoryOptions = ref<Array<{ label: string; value: number }>>([]);

// 加载父分类选项
async function loadParentCategories() {
  try {
    const res = await getAllCategories();
    if (res && res.data && Array.isArray(res.data)) {
      parentCategoryOptions.value = res.data
        .filter(cat => cat.isActive) // 只显示启用的分类
        .map(cat => ({
          label: cat.name,
          value: cat.id,
        }));
    } else {
      parentCategoryOptions.value = [];
    }
  } catch (error) {
    console.error('加载父分类失败:', error);
    parentCategoryOptions.value = [];
  }
}

// 定义表单配置
const formSchema = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入分类名称',
      maxlength: 100,
      showCount: true,
    },
    fieldName: 'name',
    label: '分类名称',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入分类标识（如：tech-news）',
      maxlength: 50,
      showCount: true,
    },
    fieldName: 'slug',
    label: '分类标识',
    rules: 'required',
    help: '用于URL路径，只能包含小写字母、数字和连字符',
  },
  {
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入分类描述',
      maxlength: 500,
      showCount: true,
      rows: 3,
    },
    fieldName: 'description',
    label: '分类描述',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择父分类（可选）',
      options: [],
      allowClear: true,
    },
    fieldName: 'parent_id',
    label: '父分类',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序值',
      min: 0,
      max: 9999,
    },
    fieldName: 'sort_order',
    label: '排序',
    help: '数值越小排序越靠前',
  },
  {
    component: 'Switch',
    componentProps: {
      checkedChildren: '启用',
      unCheckedChildren: '禁用',
    },
    fieldName: 'is_active',
    label: '状态',
  },
];

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: formSchema,
  showDefaultActions: false,
});

// 重置表单
function handleReset() {
  formApi.resetForm();
  message.success('表单已重置');
}

// 提交表单
async function handleSubmit() {
  try {
    const values = await formApi.validate();
    console.log('提交分类数据:', values);

    if (isUpdate.value && id.value) {
      // 更新分类
      await updateCategory(id.value, values);
      message.success('分类更新成功');
    } else {
      // 创建分类
      await createCategory(values);
      message.success('分类创建成功');
    }

    drawerApi.close();
  } catch (error: any) {
    console.error('提交分类失败:', error);
    if (error.response?.data?.message) {
      message.error(error.response.data.message);
    } else if (error.message) {
      message.error(error.message);
    } else {
      message.error('操作失败');
    }
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadParentCategories();
  
  // 动态更新父分类选项
  if (formApi && formApi.schema && Array.isArray(formApi.schema)) {
    const parentField = formApi.schema.find(item => item.fieldName === 'parent_id');
    if (parentField && parentField.componentProps) {
      parentField.componentProps.options = parentCategoryOptions.value;
    }
  }
});

defineExpose({
  open: drawerApi.open,
  setData: drawerApi.setData,
});
</script>

<template>
  <Drawer
    :title="title"
    :width="600"
    class="category-form-drawer"
  >
    <Form />
    
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button @click="handleReset">
          重置
        </Button>
        <Button @click="drawerApi.close">
          取消
        </Button>
        <Button type="primary" @click="handleSubmit">
          {{ isUpdate ? '更新' : '创建' }}
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
.category-form-drawer :deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
