<script setup lang="ts">
import type {
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { $t } from '@vben/locales';

import { Button, message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteCategory,
  getCategoryList,
  type NewsManagementApi,
} from '#/api/news-management';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

defineOptions({
  name: 'NewsCategoryList',
});

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: useColumns(),
  formConfig: {
    enabled: true,
    items: useGridFormSchema(),
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page, form }) => {
        const params = {
          page: page.currentPage,
          pageSize: page.pageSize,
          search: form.search || undefined,
          parentId: form.parentId || undefined,
          isActive: form.isActive,
        };

        const response = await getCategoryList(params);
        
        return {
          page: {
            total: response.data.pagination.total,
          },
          result: response.data.list,
        };
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  checkboxConfig: {
    reserve: true,
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    refreshOptions: { code: 'query' },
    search: true,
    zoom: true,
    zoomOptions: {},
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

// 新增分类
function onCreate() {
  formDrawerApi.open();
}

// 编辑分类
function onEdit(row: NewsManagementApi.NewsCategory) {
  console.log('编辑分类:', row);
  const editData = {
    ...row,
    parent_id: row.parentId,
    sort_order: row.sortOrder,
    is_active: row.isActive,
  };
  formDrawerApi.setData(editData).open();
}

// 删除分类
function onDelete(row: NewsManagementApi.NewsCategory) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${row.name}"吗？删除后不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await deleteCategory(row.id);
        message.success('分类删除成功');
        gridApi.grid?.commitProxy('query');
      } catch (error: any) {
        console.error('删除分类失败:', error);
        message.error(error.response?.data?.message || '删除分类失败');
      }
    },
  });
}

// 操作按钮点击处理
function onActionClick({ code, row }: { code: string; row: NewsManagementApi.NewsCategory }) {
  switch (code) {
    case 'edit':
      onEdit(row);
      break;
    case 'delete':
      onDelete(row);
      break;
    default:
      break;
  }
}

// 设置操作处理器
gridOptions.onActionClick = onActionClick;
</script>

<template>
  <Page auto-content-height>
    <FormDrawer :on-success="() => gridApi.grid?.commitProxy('query')" />
    <Grid :table-title="'分类管理'">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', ['分类']) }}
        </Button>
      </template>

      <!-- 自定义分类名称显示 -->
      <template #name="{ row }">
        <div class="flex items-center gap-2">
          <span class="font-medium">{{ row.name }}</span>
          <Tag v-if="row.parentId" color="blue" size="small">子分类</Tag>
        </div>
      </template>

      <!-- 自定义状态显示 -->
      <template #isActive="{ row }">
        <Tag :color="row.isActive ? 'green' : 'red'">
          {{ row.isActive ? '启用' : '禁用' }}
        </Tag>
      </template>

      <!-- 自定义描述显示 -->
      <template #description="{ row }">
        <div class="max-w-xs truncate" :title="row.description">
          {{ row.description || '-' }}
        </div>
      </template>
    </Grid>
  </Page>
</template>

<style scoped>
/* 自定义样式 */
</style>
