<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNewsStore } from '@/store/news'

const route = useRoute()
const router = useRouter()
const newsStore = useNewsStore()

// 获取新闻ID
const newsId = computed(() => {
  return parseInt(route.params.id as string)
})

// 当前新闻
const currentNews = computed(() => newsStore.newsDetail)
const loading = computed(() => newsStore.detailLoading)
const error = computed(() => newsStore.detailError)

// 加载新闻详情
const loadNewsDetail = async () => {
  if (newsId.value && !isNaN(newsId.value)) {
    await newsStore.fetchNewsDetail(newsId.value)
  } else {
    newsStore.detailError = '无效的新闻ID'
  }
}

// 返回新闻列表
const goBack = () => {
  router.go(-1)
}

// 分享功能
const shareNews = () => {
  if (navigator.share) {
    navigator.share({
      title: currentNews.value?.title,
      text: currentNews.value?.summary,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    alert('链接已复制到剪贴板')
  }
}

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化失败:', error)
    return dateString
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadNewsDetail()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-lg text-gray-600">加载中...</span>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="text-red-500 text-lg mb-4">{{ error }}</div>
        <button 
          @click="goBack"
          class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        >
          返回
        </button>
      </div>
    </div>

    <!-- 新闻详情 -->
    <div v-else-if="currentNews" class="max-w-4xl mx-auto">
      <!-- 头部导航 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-4 py-4">
          <div class="flex items-center justify-between">
            <button 
              @click="goBack"
              class="flex items-center text-gray-600 hover:text-primary transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              返回
            </button>
            
            <button 
              @click="shareNews"
              class="flex items-center text-gray-600 hover:text-primary transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              分享
            </button>
          </div>
        </div>
      </div>

      <!-- 文章内容 -->
      <article class="bg-white">
        <!-- 封面图 -->
        <div class="relative h-64 md:h-96 overflow-hidden">
          <img 
            :src="currentNews.coverImage" 
            :alt="currentNews.title"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <!-- 文章头部信息 -->
        <div class="px-6 py-8">
          <!-- 标题 -->
          <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
            {{ currentNews.title }}
          </h1>

          <!-- 文章信息 -->
          <div class="flex flex-wrap items-center text-sm text-gray-500 mb-8 space-x-6">
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {{ currentNews.author }}
            </div>
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(currentNews.publishDate) }}
            </div>
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {{ currentNews.viewCount.toLocaleString() }} 阅读
            </div>
          </div>

          <!-- 摘要 -->
          <div class="bg-gray-50 p-6 rounded-lg mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">文章摘要</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentNews.summary }}</p>
          </div>

          <!-- 正文内容 -->
          <div class="prose prose-lg max-w-none">
            <div v-html="currentNews.content.replace(/\n/g, '<br>')"></div>
          </div>
        </div>
      </article>

      <!-- 底部操作栏 -->
      <div class="bg-white border-t px-6 py-4">
        <div class="flex items-center justify-between">
          <button 
            @click="goBack"
            class="flex items-center px-4 py-2 text-gray-600 hover:text-primary transition-colors"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            返回列表
          </button>
          
          <button 
            @click="shareNews"
            class="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            分享文章
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #1f2937;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.75rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: #374151;
}

.prose ul, .prose ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.prose em {
  font-style: italic;
}
</style>
