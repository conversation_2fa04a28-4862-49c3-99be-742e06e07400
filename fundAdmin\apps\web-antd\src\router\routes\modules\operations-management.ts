import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'carbon:settings-services',
      order: 1.5, // 在用户运营(1)和基金运营(2)之间
      title: '运营管理',
    },
    name: 'OperationsManagement',
    path: '/operations-management',
    children: [
      {
        path: '/operations-management/banners',
        name: 'BannerManagement',
        meta: {
          icon: 'carbon:image',
          title: 'Banner管理',
        },
        component: () => import('#/views/banner-management/index.vue'),
      },
      {
        path: '/operations-management/news',
        name: 'NewsManagement',
        meta: {
          icon: 'carbon:document',
          title: '新闻管理',
        },
        component: () => import('#/views/news-management/index.vue'),
      },
      {
        path: '/operations-management/news-categories',
        name: 'NewsCategoryManagement',
        meta: {
          icon: 'carbon:category',
          title: '分类管理',
        },
        component: () => import('#/views/news-management/categories/list.vue'),
      },
    ],
  },
];

export default routes;
