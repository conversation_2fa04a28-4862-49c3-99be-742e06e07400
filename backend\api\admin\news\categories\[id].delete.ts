import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员删除新闻分类接口
 * DELETE /api/admin/news/categories/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除分类'
    //   });
    // }

    // 获取分类ID
    const categoryId = getRouterParam(event, 'id');
    if (!categoryId || isNaN(Number(categoryId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的分类ID'
      });
    }

    // 检查分类是否存在
    const existingCategory = await query(
      'SELECT id, name FROM news_categories WHERE id = ?',
      [categoryId]
    );

    if (existingCategory.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '分类不存在'
      });
    }

    const categoryName = existingCategory[0].name;

    // 检查是否有新闻使用此分类
    const newsWithCategory = await query(
      'SELECT COUNT(*) as count FROM news WHERE category_id = ?',
      [categoryId]
    );

    if (newsWithCategory[0].count > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `无法删除分类，还有 ${newsWithCategory[0].count} 篇新闻使用此分类`
      });
    }

    // 检查是否有子分类
    const childCategories = await query(
      'SELECT COUNT(*) as count FROM news_categories WHERE parent_id = ?',
      [categoryId]
    );

    if (childCategories[0].count > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `无法删除分类，还有 ${childCategories[0].count} 个子分类`
      });
    }

    // 删除分类
    await query(
      'DELETE FROM news_categories WHERE id = ?',
      [categoryId]
    );

    // 记录审计日志
    await logAdminAction(admin.id, 'category:delete', '删除新闻分类', {
      categoryId,
      categoryName
    });

    return {
      success: true,
      message: '分类删除成功'
    };

  } catch (error: any) {
    logger.error('删除新闻分类失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      categoryId: getRouterParam(event, 'id'),
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '删除分类失败'
    });
  }
});
