<template>
  <div class="rich-editor-wrapper">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-group">
        <button type="button" @click="execCommand('bold')" :class="{ active: isActive('bold') }" title="粗体">
          <strong>B</strong>
        </button>
        <button type="button" @click="execCommand('italic')" :class="{ active: isActive('italic') }" title="斜体">
          <em>I</em>
        </button>
        <button type="button" @click="execCommand('underline')" :class="{ active: isActive('underline') }" title="下划线">
          <u>U</u>
        </button>
        <button type="button" @click="execCommand('strikeThrough')" :class="{ active: isActive('strikeThrough') }" title="删除线">
          <s>S</s>
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-group">
        <select @change="execCommand('fontSize', $event.target.value)" class="toolbar-select">
          <option value="">字号</option>
          <option value="1">小</option>
          <option value="3">正常</option>
          <option value="5">大</option>
          <option value="7">特大</option>
        </select>

        <select @change="execCommand('formatBlock', $event.target.value)" class="toolbar-select">
          <option value="">格式</option>
          <option value="p">正文</option>
          <option value="h1">标题1</option>
          <option value="h2">标题2</option>
          <option value="h3">标题3</option>
          <option value="h4">标题4</option>
        </select>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-group">
        <button type="button" @click="execCommand('justifyLeft')" :class="{ active: isActive('justifyLeft') }" title="左对齐">
          ⬅
        </button>
        <button type="button" @click="execCommand('justifyCenter')" :class="{ active: isActive('justifyCenter') }" title="居中">
          ↔
        </button>
        <button type="button" @click="execCommand('justifyRight')" :class="{ active: isActive('justifyRight') }" title="右对齐">
          ➡
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-group">
        <button type="button" @click="execCommand('insertUnorderedList')" :class="{ active: isActive('insertUnorderedList') }" title="无序列表">
          ⋅⋅⋅
        </button>
        <button type="button" @click="execCommand('insertOrderedList')" :class="{ active: isActive('insertOrderedList') }" title="有序列表">
          1.2.3
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-group">
        <button type="button" @click="insertLink" title="插入链接">
          🔗
        </button>
        <button type="button" @click="insertImage" title="插入图片">
          🖼️
        </button>
        <input
          ref="fileInputRef"
          type="file"
          accept="image/*"
          @change="handleImageUpload"
          style="display: none;"
        />
      </div>

      <div class="toolbar-divider"></div>

      <div class="toolbar-group">
        <button type="button" @click="execCommand('undo')" title="撤销">
          ↶
        </button>
        <button type="button" @click="execCommand('redo')" title="重做">
          ↷
        </button>
        <button type="button" @click="execCommand('removeFormat')" title="清除格式">
          🧹
        </button>
      </div>
    </div>

    <!-- 编辑区域 -->
    <div
      ref="editorRef"
      class="editor-content"
      contenteditable="true"
      :style="{ minHeight: typeof height === 'number' ? height + 'px' : height }"
      :data-placeholder="placeholder"
      @input="handleInput"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
      @keydown="handleKeydown"
      @paste="handlePaste"
      @focus="handleFocus"
      @blur="handleBlur"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';

interface Props {
  modelValue?: string;
  placeholder?: string;
  readonly?: boolean;
  height?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  readonly: false,
  height: '300px',
});

const emits = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

const editorRef = ref<HTMLElement>();
const fileInputRef = ref<HTMLInputElement>();
let isUpdatingFromProp = false;
let isComposing = false; // 输入法组合状态

// 执行编辑命令
function execCommand(command: string, value?: string) {
  if (!editorRef.value) return;

  editorRef.value.focus();
  document.execCommand(command, false, value || '');
  updateContent();
}

// 检查命令状态
function isActive(command: string): boolean {
  try {
    return document.queryCommandState(command);
  } catch {
    return false;
  }
}

// 插入链接
function insertLink() {
  const url = prompt('请输入链接地址:');
  if (url) {
    execCommand('createLink', url);
  }
}

// 插入图片
function insertImage() {
  fileInputRef.value?.click();
}

// 处理图片上传
function handleImageUpload(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  // 创建图片预览
  const reader = new FileReader();
  reader.onload = (e) => {
    const img = `<img src="${e.target?.result}" style="max-width: 100%; height: auto;" />`;
    execCommand('insertHTML', img);
  };
  reader.readAsDataURL(file);

  // 清空input
  target.value = '';
}

// 更新内容
function updateContent() {
  if (isUpdatingFromProp || !editorRef.value || isComposing) return;

  const content = editorRef.value.innerHTML;
  emits('update:modelValue', content);
  emits('change', content);
}

// 处理输入
function handleInput() {
  if (!isComposing) {
    updateContent();
  }
}

// 处理输入法开始
function handleCompositionStart() {
  isComposing = true;
}

// 处理输入法结束
function handleCompositionEnd() {
  isComposing = false;
  updateContent();
}

// 处理键盘事件
function handleKeydown(event: KeyboardEvent) {
  // Ctrl+Z 撤销
  if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
    event.preventDefault();
    execCommand('undo');
  }
  // Ctrl+Y 或 Ctrl+Shift+Z 重做
  else if (event.ctrlKey && (event.key === 'y' || (event.key === 'z' && event.shiftKey))) {
    event.preventDefault();
    execCommand('redo');
  }
  // Ctrl+B 粗体
  else if (event.ctrlKey && event.key === 'b') {
    event.preventDefault();
    execCommand('bold');
  }
  // Ctrl+I 斜体
  else if (event.ctrlKey && event.key === 'i') {
    event.preventDefault();
    execCommand('italic');
  }
  // Ctrl+U 下划线
  else if (event.ctrlKey && event.key === 'u') {
    event.preventDefault();
    execCommand('underline');
  }
}

// 处理粘贴
function handlePaste(event: ClipboardEvent) {
  event.preventDefault();
  const text = event.clipboardData?.getData('text/html') || event.clipboardData?.getData('text/plain') || '';
  if (text) {
    execCommand('insertHTML', text);
  }
}

// 处理焦点
function handleFocus() {
  // 可以添加焦点处理逻辑
}

function handleBlur() {
  updateContent();
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (isUpdatingFromProp || !editorRef.value || isComposing) return;

  isUpdatingFromProp = true;
  nextTick(() => {
    if (editorRef.value && newValue !== editorRef.value.innerHTML) {
      editorRef.value.innerHTML = newValue || '';
    }
    isUpdatingFromProp = false;
  });
});

// 监听只读状态变化
watch(() => props.readonly, (newValue) => {
  if (editorRef.value) {
    editorRef.value.contentEditable = (!newValue).toString();
  }
});

// 初始化
onMounted(() => {
  nextTick(() => {
    if (editorRef.value) {
      editorRef.value.contentEditable = (!props.readonly).toString();
      if (props.modelValue) {
        editorRef.value.innerHTML = props.modelValue;
      }
      if (!props.modelValue && props.placeholder) {
        editorRef.value.setAttribute('data-placeholder', props.placeholder);
      }
    }
  });
});

onBeforeUnmount(() => {
  // 清理工作
});

// 暴露方法
defineExpose({
  getHTML: () => editorRef.value?.innerHTML || '',
  getText: () => editorRef.value?.textContent || '',
  setHTML: (html: string) => {
    if (editorRef.value) {
      editorRef.value.innerHTML = html;
      updateContent();
    }
  },
  focus: () => editorRef.value?.focus(),
  blur: () => editorRef.value?.blur(),
  execCommand,
});
</script>

<style scoped>
.rich-editor-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.rich-editor-wrapper:hover {
  border-color: #4096ff;
}

.rich-editor-wrapper:focus-within {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-group button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.toolbar-group button:hover {
  background-color: #f0f0f0;
  border-color: #4096ff;
}

.toolbar-group button.active {
  background-color: #e6f4ff;
  border-color: #4096ff;
  color: #4096ff;
}

.toolbar-select {
  height: 32px;
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  cursor: pointer;
}

.toolbar-select:hover {
  border-color: #4096ff;
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background-color: #d9d9d9;
  margin: 0 4px;
}

.editor-content {
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  outline: none;
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
}

.editor-content:empty::before {
  content: attr(data-placeholder);
  color: #bfbfbf;
  pointer-events: none;
}

/* 编辑器内容样式 */
.editor-content h1 {
  font-size: 24px;
  font-weight: bold;
  margin: 16px 0 8px 0;
}

.editor-content h2 {
  font-size: 20px;
  font-weight: bold;
  margin: 14px 0 7px 0;
}

.editor-content h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 12px 0 6px 0;
}

.editor-content h4 {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.editor-content p {
  margin: 8px 0;
}

.editor-content ul, .editor-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.editor-content li {
  margin: 4px 0;
}

.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.editor-content a {
  color: #4096ff;
  text-decoration: underline;
}

.editor-content a:hover {
  color: #1677ff;
}

.editor-content blockquote {
  border-left: 4px solid #d9d9d9;
  padding-left: 12px;
  margin: 12px 0;
  color: #666;
  font-style: italic;
}
</style>
