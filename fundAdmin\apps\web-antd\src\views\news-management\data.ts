import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NewsManagementApi } from '#/api/news-management';

import { h } from 'vue';

import { Tag, Image, Button, Space } from 'ant-design-vue';

// 新闻状态配置
export const NEWS_STATUS_CONFIG = {
  draft: { color: 'default', text: '草稿' },
  pending: { color: 'processing', text: '待审核' },
  published: { color: 'success', text: '已发布' },
  archived: { color: 'warning', text: '已归档' },
};

// 新闻状态选项
export const NEWS_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '草稿', value: 'draft' },
  { label: '待审核', value: 'pending' },
  { label: '已发布', value: 'published' },
  { label: '已归档', value: 'archived' },
];

// 表格列配置
export function useColumns(onActionClick: (params: any) => void): VxeGridProps['columns'] {
  return [
    {
      type: 'checkbox',
      width: 50,
    },
    {
      title: 'ID',
      field: 'id',
      width: 80,
      sortable: true,
    },
    {
      title: '封面图',
      field: 'coverImage',
      width: 100,
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          if (row.coverImage) {
            return h(Image, {
              src: row.coverImage,
              width: 60,
              height: 40,
              style: { objectFit: 'cover', borderRadius: '4px' },
              preview: true,
            });
          }
          return h('span', { style: { color: '#999' } }, '无图片');
        },
      },
    },
    {
      title: '标题',
      field: 'title',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      title: '摘要',
      field: 'summary',
      minWidth: 180,
      showOverflow: 'tooltip',
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          return row.summary || h('span', { style: { color: '#999' } }, '无摘要');
        },
      },
    },
    {
      title: '分类',
      field: 'category',
      minWidth: 120,
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          return row.category ?
            h(Tag, { color: 'blue' }, () => row.category!.name) :
            h('span', { style: { color: '#999' } }, '未分类');
        },
      },
    },
    {
      title: '作者',
      field: 'author',
      minWidth: 100,
      showOverflow: 'tooltip',
    },
    {
      title: '状态',
      field: 'status',
      minWidth: 100,
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          const config = NEWS_STATUS_CONFIG[row.status];
          return h(Tag, { color: config.color }, () => config.text);
        },
      },
    },
    {
      title: '推荐',
      field: 'isFeatured',
      minWidth: 80,
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          return row.isFeatured ?
            h(Tag, { color: 'gold' }, () => '推荐') :
            h('span', { style: { color: '#999' } }, '普通');
        },
      },
    },
    {
      title: '阅读量',
      field: 'viewCount',
      width: 100,
      sortable: true,
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          return h('span', { style: { color: '#1890ff' } }, row.viewCount.toLocaleString());
        },
      },
    },
    {
      title: '发布时间',
      field: 'publishDate',
      width: 160,
      sortable: true,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : '-';
      },
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 160,
      sortable: true,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return new Date(cellValue).toLocaleString();
      },
    },
    {
      title: '操作',
      field: 'actions',
      width: 260,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: NewsManagementApi.News }) => {
          return h(Space, { size: 'small' }, () => [
            h(Button, {
              type: 'link',
              size: 'small',
              onClick: () => onActionClick({ action: 'view', row }),
            }, () => '查看'),

            h(Button, {
              type: 'link',
              size: 'small',
              onClick: () => onActionClick({ action: 'edit', row }),
            }, () => '编辑'),

            h(Button, {
              type: 'link',
              size: 'small',
              danger: true,
              onClick: () => onActionClick({ action: 'delete', row }),
            }, () => '删除'),

            row.status === 'published' ?
              h(Button, {
                type: 'link',
                size: 'small',
                danger: true,
                onClick: () => onActionClick({ action: 'unpublish', row }),
              }, () => '下线') :
              h(Button, {
                type: 'link',
                size: 'small',
                onClick: () => onActionClick({ action: 'publish', row }),
              }, () => '发布'),
          ]);
        },
      },
    },
  ];
}

// 搜索表单配置
export function useGridFormSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入新闻标题或内容关键词',
        clearable: true,
      },
      fieldName: 'search',
      label: '关键词搜索',
    },
    {
      component: 'Select',
      componentProps: {
        options: NEWS_STATUS_OPTIONS,
        placeholder: '请选择状态',
        clearable: true,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入作者名称',
        clearable: true,
      },
      fieldName: 'author',
      label: '作者',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: '' },
          { label: '推荐', value: 'true' },
          { label: '普通', value: 'false' },
        ],
        placeholder: '请选择推荐状态',
        clearable: true,
      },
      fieldName: 'featured',
      label: '推荐状态',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始日期', '结束日期'],
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'dateRange',
      label: '创建时间',
    },
  ];
}

// 新闻表单配置
export function useFormSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入新闻标题',
        maxlength: 255,
        showCount: true,
      },
      fieldName: 'title',
      label: '新闻标题',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入新闻摘要',
        maxlength: 500,
        showCount: true,
        rows: 3,
      },
      fieldName: 'summary',
      label: '新闻摘要',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择新闻分类',
        options: [], // 动态加载
      },
      fieldName: 'category_id',
      label: '新闻分类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入作者名称',
      },
      fieldName: 'author',
      label: '作者',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入来源链接',
        type: 'url',
      },
      fieldName: 'source_url',
      label: '来源链接',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '草稿', value: 'draft' },
          { label: '待审核', value: 'pending' },
          { label: '已发布', value: 'published' },
        ],
        placeholder: '请选择状态',
      },
      fieldName: 'status',
      label: '状态',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      fieldName: 'is_featured',
      label: '是否推荐',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择发布时间',
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'publish_date',
      label: '发布时间',
    },
  ];
}
